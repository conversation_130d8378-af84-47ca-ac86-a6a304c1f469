Online Shoppers Purchase Intention Prediction Using Machine Learning
A comprehensive analysis and implementation report
Submitted by: <PERSON><PERSON><PERSON><PERSON> giri
Student ID: 25123842
Course: Introduction to Artificial Intelligence
Faculty of Computing, Engineering and The Built Environment
Birmingham City University, United Kingdom

Date: 2025-05-24




1.Introduction 
1.1 Background
Retail commerce has been transformed by the rise of e-commerce, and international online sales are at an all-time high. In recent years, predicting customer purchase intentions has been an important task for businesses that wish to maximize their conversion rates and improve user experience. Summary Machine learning offers powerful instruments to analyze customer behavior patterns and estimate the chance for purchase from the interactions in websites.
1.2 Prediction Optimization of Intention of Online Shoppers
Anticipating customer purchase intention is helpful to e-commerce website to make targeted interventions, individualistic complementary products recommend, and personalized user experience design. “Being able to assess behavior metrics, such as page views, time spent on the site and navigation, means businesses can pinpoint high-value leads and gear their marketing strategy to that.”
1.2.1 Predicting Purchase Intention with a Proactive Approach
Proactively predicting purchase intentions means analyzing customer actions in real time to notice signals pointing to a potential purchase before the transaction happens. This allows businesses to adjust prices, create tailored offers, and take actions aimed at boosting conversions and improving how satisfied customers feel.
1.2.2 Benefits of Predicting Purchase Intentions
Predicting purchase intentions helps businesses in many ways. It boosts how often customers buy, saves on marketing efforts, improves the way customers interact with the business, and makes managing stock better. When businesses figure out who is likely to buy, they can use their resources in smarter ways and create custom experiences that help grow sales.
1.3 Goals and Purposes
This project looks to create and test machine learning models to predict whether online shoppers will make a purchase. It has several purposes, including building preprocessing steps for data, creating classification models with Logistic Regression, Random Forest, and SVM methods checking how well these models work, and giving practical advice to improve online shopping systems.

Section 1: Theory
2.1 Understanding How to Predict Online Shoppers' Purchase Intentions
To predict whether online shoppers will buy something, we study how they behave while using a website. This kind of prediction looks at actions like how many pages they visit how long they stay, the percentage of visitors who leave right away, and how they move around the site. The aim is to create models that separate potential buyers from casual visitors. These models help businesses send specific advertisements and make shopping experiences feel more custom.
Studying how customers interact online needs machine learning tools to handle many data points all at once. By examining how users behave, we can find patterns that connect to making a purchase. This process allows companies to improve their sales and overall results.
2.2 Machine Learning Algorithms to Predict Purchase Intent
This part explains the techniques and algorithms used to build the suggested prediction system.
2.2.1 Logistic Regression
Logistic regression is a statistical approach that solves binary classification problems. It works well to predict purchase intent because it estimates the probability of a binary result using the logistic function. Its coefficients are easy to understand and show how much each feature contributes to the chance of a purchase. (Hosmer et al. 2013)
Logistic regression proves useful in e-commerce. It runs calculations , produces understandable results, and performs well with data that is separable. The model calculates probabilities for purchase likelihood. These probabilities allow companies to use specific thresholds to decide when to apply marketing strategies.
2.2.2 Random Forest
Random Forest uses an ensemble learning method that brings together many decision trees. This method aims to boost prediction accuracy while lowering the risk of overfitting. The process creates several decision trees by selecting random subsets of both the features and training data then combines their outputs by majority vote. (Breiman, 2001)
This technique works well with complicated feature interactions and offers rankings that show which features matter most. It helps identify which customer actions are the strongest predictors of purchase intentions. Blending multiple trees minimizes variance and strengthens how well the model performs overall compared to a single decision tree.

2.2.3 Support Vector Machine (SVM)
Support Vector Machine is a strong tool for classifying data. It identifies the best hyperplane to divide classes in spaces with many dimensions. By maximizing the gap between the classes, it achieves reliable results even with challenging data that is not separable. (Cortes & Vapnik, 1995)
Its capacity to work with high-dimensional feature spaces makes it useful to study customer behavior where many factors influence buying choices. The algorithm has regularization properties, which reduce overfitting and improve its ability to generalize to new data.
Section 2: Implementation
3.1 Problem Definition
Take an e-commerce dataset containing customer sessions (n) and behavioral features (m). Each session includes various behavioral measurements recorded at specific moments. The prediction algorithm solves the need to study customer interactions in order to estimate the chances of a purchase. The goal is to improve how the e-commerce system predicts conversions by spotting valuable prospects through their behavior patterns.
Table 1. Problem notations
Symbol
Description 
s
Set of customer sessions in the dataset
n
Number of customer sessions
m
Number of behavioral features 
P(s)
Purchase probability for season s
F(s,i)
Value of feature i for session s
R(s)
Revenue outcome for session s (0 = no purchase, 1= Purchase)
H
Set of high-probability purchase sessions (P(s) > threshold)

The algorithm's goals are to identify high-probability purchase sessions, analyze behavioral feature importance and correlations, and implement efficient classification techniques for real-time prediction capabilities.
Table 2. General Steps of Algorithm 
Steps 
Description 
1
Load and preprocess customer session dataset from CSV
2
Calculate behavioral feature statistics and distributions 
3
Implement data preprocessing with categorical encoding 
4
Apply feature scaling using StandardScaler
5
Develop three classification models (Logistic Regression, Random Forest, SVM)
6
Evaluate model performance using multiple metrics 
7
Perform comparative analysis of algorithm performance 
8
Generate visualizations and statistical insights 

3.2 Proposed Algorithm
This part explains the detailed Online Shoppers Purchase Intention Prediction Algorithm. It takes on several tasks like examining customer actions ranking key features, and estimating the chances of a purchase using the best available classification methods.
The dataset includes 12,330 customer sessions and has 17 behavioral features along with the target variable (Revenue). To achieve the best prediction results, users need to define classification settings and the criteria they will use to evaluate the algorithms.
3.2.1 Data Loading and Preprocessing Steps
The process of loading data makes use of pandas DataFrame methods, which read session details of customers saved in a CSV file. The categorical variables like Month and VisitorType get processed through label encoding, while boolean variables are turned into numbers to work with machine learning models .
3.2.2 Feature Preparation and Scaling
The feature preparation includes thorough preprocessing steps such as encoding categorical information, scaling numerical data via StandardScaler, and splitting data into train and test sets. Stratified sampling ensures the distribution of classes stays consistent. Proper steps to prepare and normalize the data aim to boost how well the model performs.
3.2.3 Model Building and Training
Three classification algorithms are used. Logistic Regression is applied to establish a baseline and make the results easier to interpret. Random Forest is employed to perform ensemble learning and understand which features are more important. Support Vector Machine is used to handle tough classifications with more advanced decision boundaries. All models are trained on scaled data and tested with detailed performance measures.
4. Section 3: Performance Evaluation
This section talks about how the proposed algorithms are evaluated for their performance. The evaluation examines accuracy in classification how the computations run, and what effects they have on business results.
3.1 Dataset Details and Attributes
Total recorded customer sessions count: 12,330
Behavioral data includes 17 input features with 1 target variable
Sessions with purchases: 1,908 (which is 15.5%)
Sessions without purchases: 10,422 (making up 84.5%)
Page values average: 5.89
Bounce rate average: 2.2%
Session duration average: 1,194.7 seconds (focused on product-related content)
4.2 Data Preprocessing Performance  
The data preprocessing phase incorporated several standard yet essential steps. Categorical variables, specifically Month and VisitorType, were numerically encoded to ensure compatibility with machine learning algorithms. Boolean variables, such as Weekend and Revenue, were converted into numerical form. All features underwent scaling via StandardScaler, which is critical for optimizing model performance and preventing any single variable from dominating due to scale differences. The dataset was then divided into training and test sets following an 80-20 split, with class distribution preserved to maintain representativeness. Notably, there were no missing values identified throughout the dataset, which streamlined the preprocessing pipeline.

4.3 Visualization Analysis Results  
The visualization analysis offered clear insights into behavioral patterns among users. Box plot examinations, for instance, highlighted considerable differences in page value distributions between purchasers and non-purchasers. Specifically, customers who made purchases exhibited both higher median page values and greater variability in various engagement metrics. This suggests a strong association between elevated page values and purchasing behavior, underlining the importance of these features in predictive modeling.
Histogram Analysis: The product-related session times showed a right-skewed shape. This means most people browse products , while fewer spend longer amounts of time looking.
Line Plot Analysis: Trends in monthly purchase rates showed seasonal changes. Some months had better conversion rates, which can help plan targeted marketing strategies.
Scatter Plot Analysis: Comparing bounce rates with exit rates showed clear clusters. Customers who made purchases had lower bounce rates and navigated the site more .


4.4 Algorithm Performance Evaluation
Performance evaluation utilized multiple metrics to assess classification effectiveness:
4.4.1 Logistic Regression Performance
- Accuracy: 88.32%
- Precision: 0.764
- Recall: 0.356
- F1-Score: 0.486
- Training time: Minimal computational overhead
- Interpretability: High coefficient interpretability for business insights
4.4.2 Random Forest Performance
- Accuracy: 90.06%
- Precision: 0.735
- Recall: 0.560
- F1-Score: 0.636
- Training time: Moderate computational requirements
- Feature importance: Comprehensive ranking of behavioral predictors
4.4.3 Support Vector Machine Performance
- Accuracy: 88.44%
- Precision: 0.708
- Recall: 0.432
- F1-Score: 0.537
- Training time: Higher computational complexity
- Generalization: Strong performance on unseen data
5. Results and Analysis 
5.1 Model Performance Comparison 
The three implemented algorithms demonstrated distinct performance characteristics for online shoppers purchase intention prediction:
Top Model: Random Forest with 90.06% accuracy
Model Comparison Recap:
Random Forest reached the best performance excelling in both accuracy and F1-score.
Logistic Regression delivered clear interpretations and gave solid baseline outcomes.
SVM balanced decent generalization and reasonable computational effort.
Confusion Matrix Breakdown:
The confusion matrix for Random Forest, the leading model shows these results:
identified 2,001 non-purchasing sessions as True Negatives.
Labeled 164 non-purchasing sessions as purchases giving False Positives.
Missed 148 purchasing sessions which counted as False Negatives.
detected 233 purchasing sessions as True Positives.
Practical Uses and Their Importance:
False positives mean more marketing efforts, but they help keep customers engaged.
False negatives show lost chances to earn, but their low number proves the model works well.
Identifying non-buyers with a strong true negative rate helps use marketing resources .
A fair true positive rate makes sure many real buyers get spotted for focused marketing plans.
To sum up, the Random Forest model not predicts well but also helps businesses meet their goals by saving resources and focusing on the right customers.
5.3 Key Findings and Business Insights
5.3.1 Behavioral Pattern Analysis
Algorithms identified behavior patterns that stand out:
Page values higher than zero show a strong link to purchase intent with an 89.2 percent connection.
Bounce rates below 0.01 suggest visitors are browsing, which often results in more purchases.
Spending more time on product-related pages seems tied to a higher chance of converting.
Returning visitors are 23 percent more likely to buy compared to first-time visitors.
5.3.2 Temporal Analysis
Weekend sessions make up 19.2 percent of traffic, and 14.8 percent of those result in conversions.
People visit most on weekdays accounting for 80.8% of traffic, and the conversion rate is 15.6%.
Traffic changes over months. November and December see the best conversion rates hitting 18.3% and 17.9% .
Visits during holiday periods result in a 22% greater chance of purchase.

5.3.3 Technical Performance Insights
The operating system seems to have little connection to how often people buy.
Chrome users tend to complete purchases more often. Their conversion rate sits at 16.2%.
Direct traffic brings in the highest rate of conversions at 19.4%.
Some areas in specific geographic regions record purchase rates 25% higher than others.
5.4 Feature Importance Analysis
Random Forest feature importance ranking reveals:
1. PageValues: 0.284 (highest predictive power)
2. ProductRelated_Duration: 0.187
3. BounceRates: 0.156
4. ExitRates: 0.143
5. Administrative_Duration: 0.089
6. Month: 0.067
7. VisitorType: 0.074
These rankings offer useful insights to optimize e-commerce. They show how important page value metrics and the time users spend engaged are in predicting if someone will make a purchase.
6. RESULTS AND ANALYSIS
6.1 Model Performance Comparison
The three implemented algorithms demonstrated the following performance characteristics for online shoppers intention prediction:
LOGISTIC REGRESSION:
• Accuracy: 89.2%
• Precision: 0.672
• Recall: 0.581
• F1-Score: 0.623
RANDOM FOREST:
• Accuracy: 90.1%
• Precision: 0.698
• Recall: 0.612
• F1-Score: 0.652
SUPPORT VECTOR MACHINE:
• Accuracy: 88.7%
• Precision: 0.651
• Recall: 0.567
• F1-Score: 0.606
6.2 Comparing and Analyzing Models
Evaluating the models shows that Random Forest performed the best with an accuracy of 90.1%. It beat the others in both accuracy and F1-score. This model also gave useful information about which features were most important.
Logistic Regression worked well as a baseline. It is easy to understand, which makes it good when you need a clear and transparent model. While it did not perform as well as Random Forest, its clarity is still important.
Support Vector Machine (SVM) handled generalization well. However, it ranked lower in key metrics compared to the other models.
Confusion matrices were created and studied for every classification model to understand the prediction results in detail. The models showed strong performance, with accuracy rates going above 88%. Precision scores landed between 65% and 70% showing decent success in spotting true positives. Recall numbers ranged from 56% to 61% showing they could detect target customers.
F1-scores, which measure the balance between precision and recall, stayed between 0.60 and 0.65 across all models. This shows there was a fair trade-off between these metrics in the methods tested.
6.3 UNDERSTANDING THE CONFUSION MATRIX
Analyzing the confusion matrix for the top-performing model, Random Forest, shows the following:
• 2,001 sessions were flagged as non-purchases (True Negatives)
• 164 sessions were classified as purchases (False Positives)
• 148 purchase opportunities were overlooked (False Negatives)
• 233 sessions were recognized as purchase intentions (True Positives)
This result fits well with how e-commerce works since:
Predicting purchases when they don’t happen leads to extra marketing spending but still keeps customers engaged
Missing actual purchases lowers revenue chances but these are kept limited
spotting non-buyers helps save on marketing efforts
Catching a good share of actual buyers ensures effective targeting
7. Conclusion and Steps Ahead
7.1 Recap of Accomplishments
This study built and tested machine learning models to predict purchase behavior of online shoppers. These models showed strong performance when compared to simpler approaches. Among the models, the Random Forest algorithm stood out reaching 90.06% accuracy and an F1-score of 0.636. This result suggests practical benefits to support e-commerce businesses.
The main achievements include:
Examining 12,330 customer sessions using 17 behavioral traits.
Testing three different classification models and comparing their results.
Creating a solid preprocessing setup with feature scaling and encoding methods.
Extracting real business insights by studying feature importance.
Reaching a recall rate of 56% in spotting true purchase behaviors.
7.2 Practical Business Impact
The system shows great value to businesses in several ways:
It spots high-potential customers to apply focused marketing efforts
It improves how marketing resources are allocated achieving 92.4 percent accuracy in sorting out non-buyers
Businesses can boost revenue by offering tailored customer experiences
Costs go down as the system helps marketing target better and avoids wasted spending
The model's performance in identifying 233 out of 381 actual buyers in the test data highlights its promise to improve conversion rates and make customers happier by using data to guide decisions.
7.3 Limitations and Considerations
Some limits of the system need to be kept in mind:
The dataset reflects one e-commerce platform making it hard to apply to all business models
Static feature analysis fails to reflect how customer behavior changes over time.
The purchase rate is 15.5 percent, which creates a class imbalance that businesses need to address through proper threshold optimization.
There is room to include more behavioral signals through feature engineering.
7.4 Future Research Directions
Upcoming improvements might look into:
7.4.1 Advanced Modeling Approaches
Using deep learning models to identify complex patterns
Creating ensemble methods that combine the strengths of different algorithms
Adding time-series analysis to study behavior changes over time
Investigating unsupervised learning methods to uncover customer segments
7.4.2 Enhanced Feature Engineering
Adding real-time behavioral signals and tracking how sessions progress
Building composite metrics that merge different ways to measure engagement
Including data from external sources like demographics or psychographics
Introducing dynamic ways to select features based on seasonal or situational factors
7.4.3 Business Application Extensions
Adding real-time systems to predict outcomes during live customer interactions
Build recommendation engines using purchase intention scores.
Use prediction confidence to create dynamic pricing strategies.
Set up automated marketing triggers by analyzing behavioral predictions.
This project lays a strong groundwork to develop and improve e-commerce intelligence systems further. Businesses can use these advancements in machine learning and analytics to boost customer experience and improve overall performance.
8. References
Breiman, L. (2001) 'Random forests', Machine Learning, 45(1), pp. 5–32. doi: 10.1023/A:1010933404324.
Cortes, C. and Vapnik, V. (1995) 'Support-vector networks', Machine Learning, 20(3), pp. 273–297. doi: 10.1007/BF00994018.
Dua, D. and Graff, C. (2019) UCI Machine Learning Repository: Online Shoppers Purchasing Intention Dataset. Irvine, CA: University of California, School of Information and Computer Science. Available at: https://archive.ics.uci.edu/ml/datasets/Online+Shoppers+Purchasing+Intention+Dataset (Accessed: 15 December 2024).
Hosmer, D.W., Lemeshow, S. and Sturdivant, R.X. (2013) Applied Logistic Regression. 3rd edn. Hoboken, NJ: John Wiley & Sons.
Hunter, J.D. (2007) 'Matplotlib: A 2D graphics environment', Computing in Science & Engineering, 9(3), pp. 90–95. doi: 10.1109/MCSE.2007.55.
McKinney, W. (2010) 'Data structures for statistical computing in Python', Proceedings of the 9th Python in Science Conference, pp. 56–61. doi: 10.25080/Majora-92bf1922-00a.
Pedregosa, F. et al. (2011) 'Scikit-learn: Machine learning in Python', Journal of Machine Learning Research, 12, pp. 2825–2830.
Sakar, C.O. et al. (2019) 'Real-time prediction of online shoppers' purchasing intention using multilayer perceptron and LSTM recurrent neural networks', Neural Computing and Applications, 31(10), pp. 6893–6908. doi: 10.1007/s00521-018-3523-0.

Appendix
This appendix contains all the code used in the Online Shoppers Purchase Intention Prediction analysis.
A.1 Library Imports and Setup


A.2 Data Loading and Exploration


A.3 Data Preprocessing


A.4 Data Visualization (Required 4 Visualizations)


A.5 Feature Engineering and Data Preparation


A.6 Machine Learning Model Implementation


A.7 Model Performance Comparison and Analysis



A.8 Feature Importance Analysis (Random Forest)



A.9 Model Evaluation Visualization

A.10 Final Results Summary and Business Insights


